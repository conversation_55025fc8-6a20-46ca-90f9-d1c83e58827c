{"app": {"title": "AI重器", "welcome": "欢迎使用这个跨平台的Electron应用！", "version": "版本", "loading": "加载中...", "ready": "准备就绪"}, "common": {"back": "返回", "save": "保存", "cancel": "取消", "confirm": "确认", "delete": "删除", "edit": "编辑", "add": "添加", "close": "关闭", "ok": "确定", "yes": "是", "no": "否", "loading": "加载中...", "error": "错误", "success": "成功", "warning": "警告", "info": "信息"}, "login": {"title": "AI重器 - 授权验证", "auth_code": "授权码", "placeholder": "请输入您的授权码", "verify": "验证授权码", "verifying": "正在验证授权码...", "help_text": "请输入有效的授权码以访问AI重器应用。", "demo_codes": "演示授权码：", "admin_code": "管理员: ADMIN2024", "user_code": "普通用户: RDKKQF76AVX6WP4E", "error": {"empty": "请输入授权码", "invalid": "授权码无效", "network": "网络连接失败，请检查后端服务是否运行"}}, "main": {"welcome": "欢迎，{username}！", "buttons": {"admin_panel": "管理面板", "settings": "设置", "check_updates": "检查更新", "about": "关于", "logout": "退出登录", "dev_tools": "开发者工具"}, "features": {"title": "功能特性", "cross_platform": "跨平台支持 (Windows, macOS, Linux)", "auto_update": "自动更新功能", "github_actions": "GitHub Actions 自动构建", "modern_ui": "现代化的用户界面", "dev_friendly": "开发者友好的配置", "admin_features": "管理员功能 (品牌管理、用户管理)"}, "status": {"checking_updates": "正在检查更新...", "dev_mode_no_update": "开发模式下无法检查更新", "update_failed": "检查更新失败", "opening_admin": "正在打开管理面板...", "opening_settings": "正在打开设置页面...", "settings_opened": "设置页面已打开", "logging_out": "正在退出登录...", "logged_out": "已退出登录", "dev_tools_configured": "开发者工具已在主进程中配置"}, "logout": {"confirm": "确定要退出登录吗？"}, "about": {"title": "关于此应用", "description": "这是一个使用 Electron 构建的跨平台桌面应用程序。", "platforms": "支持 Windows、macOS 和 Linux 系统。", "features": "具有自动更新功能，可以自动获取最新版本。"}, "user_info": {"title": "用户信息", "username": "用户名: {username}", "type": "类型: {userType}", "status": "状态: {status}", "active": "活跃", "inactive": "禁用"}}, "settings": {"title": "设置", "language": {"title": "语言设置", "interface": "界面语言", "description": "选择应用程序的显示语言", "chinese": "中文 (简体)", "english": "English", "preview": "语言预览：当前选择的语言将立即应用到界面"}, "app": {"title": "应用设置", "startup": "开机自启动", "startup_description": "系统启动时自动运行应用", "minimize_to_tray": "最小化到系统托盘", "minimize_description": "关闭窗口时最小化到系统托盘而不是退出", "auto_update": "自动更新", "update_description": "自动检查并下载应用更新"}, "messages": {"saved": "设置已保存", "reset": "设置已重置为默认值", "language_changed": "语言已更改，重启应用后生效", "theme_changed": "主题已更改", "reset_confirm": "确定要重置所有设置为默认值吗？", "save_failed": "保存设置失败"}, "update": {"title": "更新设置", "channel": "更新通道", "channel_description": "选择应用更新的下载源", "github": "G<PERSON><PERSON>ub (默认)", "gitee": "<PERSON><PERSON><PERSON> (中国镜像)", "custom": "自定义", "custom_url": "自定义更新地址", "custom_url_description": "输入自定义的更新检查地址", "check_now": "检查更新", "check_description": "立即检查是否有可用的应用更新", "check_button": "检查更新", "checking": "检查中...", "checking_message": "正在检查更新...", "available": "发现新版本", "latest": "当前已是最新版本", "failed": "检查更新失败", "error": "检查更新时发生错误", "card": {"new_version_found": "发现新版本 {version}", "downloading": "正在下载新版本...", "download_complete": "下载完成，是否立即安装？", "download_failed": "更新失败：{error}", "preparing": "准备下载...", "installing": "正在重启安装...", "progress": "{percent}% • {speed}/s • {transferred} / {total}", "buttons": {"update_now": "立即更新", "install_now": "立即安装", "later": "稍后", "skip_version": "跳过此版本", "close": "关闭"}}}, "appearance": {"title": "外观设置", "theme": "主题", "theme_description": "选择应用程序的外观主题", "auto": "跟随系统", "light": "浅色", "dark": "深色"}, "about": {"title": "关于", "version": "应用版本", "version_description": "当前应用程序版本信息", "build_date": "构建日期: {date}"}, "actions": {"reset": "重置设置", "save": "保存设置"}, "user": {"title": "用户信息", "current_user": "当前用户"}}, "admin": {"title": "管理面板", "brand_management": "品牌管理", "user_management": "用户管理", "file_management": "文件管理", "brand_list": "品牌列表", "user_list": "用户列表", "file_list": "文件列表", "add_brand": "添加品牌", "add_user": "添加用户", "table": {"icon": "图标", "brand_name": "品牌名称", "description": "描述", "status": "状态", "actions": "操作", "username": "用户名", "email": "邮箱", "display_name": "显示名称", "type": "类型", "auth_code": "授权码", "login_count": "登录次数", "created_at": "创建时间", "preview": "预览", "file_name": "文件名", "file_type": "类型", "file_size": "大小", "uploader": "上传者", "upload_time": "上传时间"}, "file": {"stats": {"total_files": "总文件数", "total_size": "总大小", "image_files": "图片文件", "other_files": "其他文件"}, "filters": {"all_types": "所有类型", "image": "图片", "document": "文档", "video": "视频", "audio": "音频", "other": "其他", "search_placeholder": "搜索文件名...", "sort": {"newest": "最新上传", "oldest": "最早上传", "largest": "文件最大", "smallest": "文件最小", "name": "文件名"}}, "actions": {"refresh": "刷新", "cleanup": "清理", "view": "查看", "download": "下载", "delete": "删除"}, "messages": {"no_files": "暂无文件", "upload_success": "文件上传成功", "upload_failed": "文件上传失败", "delete_confirm": "确定要删除文件 \"{fileName}\" 吗？此操作不可恢复！", "delete_success": "文件删除成功", "delete_failed": "文件删除失败", "cleanup_confirm": "确定要清理无效文件吗？这将删除所有没有关联记录的文件。", "cleanup_developing": "清理功能正在开发中..."}}, "loading": "加载中...", "no_permission": "只有管理员才能访问管理面板", "login_required": "请先登录"}, "errors": {"network": "网络连接失败", "server": "服务器错误", "unauthorized": "未授权访问", "not_found": "资源未找到", "unknown": "未知错误"}, "messages": {"operation_success": "操作成功", "operation_failed": "操作失败", "data_saved": "数据已保存", "data_deleted": "数据已删除", "confirm_delete": "确定要删除吗？此操作不可撤销。"}}