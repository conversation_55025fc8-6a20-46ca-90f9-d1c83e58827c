# 开机自启动和系统托盘功能测试指南

## 问题修复说明

之前开机自启动和系统托盘功能只有设置界面，但缺少实际的功能实现。现在已经修复了以下问题：

### 修复内容

1. **开机自启动功能**
   - 添加了 `app.setLoginItemSettings()` 的实际调用
   - 在应用启动时自动应用开机自启动设置
   - 设置变更时立即生效

2. **系统托盘功能**
   - 添加了 `Tray` 对象的创建和管理
   - 实现了托盘图标和右键菜单
   - 支持最小化到托盘而不是退出应用
   - 双击托盘图标可以显示/隐藏窗口

### 修改的文件

1. `client/src/main/main.js`
   - 导入 `Tray` 模块
   - 添加托盘相关变量和函数
   - 实现开机自启动设置的应用
   - 修改窗口关闭事件处理
   - 添加 IPC 处理器来实时应用设置

2. `client/src/renderer/pages/settings.js`
   - 修改设置开关的事件处理
   - 添加立即应用设置的逻辑

3. 国际化文件
   - 添加了新的状态消息文本

## 测试步骤

### 测试开机自启动

1. 启动应用：`cd client && npm run dev`
2. 登录应用
3. 进入设置页面
4. 开启"开机自启动"开关
5. 应该看到"开机自启动设置已更新"的成功消息
6. 重启电脑验证应用是否自动启动

### 测试系统托盘

1. 启动应用：`cd client && npm run dev`
2. 登录应用
3. 进入设置页面
4. 开启"最小化到系统托盘"开关
5. 应该看到"系统托盘设置已更新"的成功消息
6. 关闭应用窗口，应用应该最小化到系统托盘而不是退出
7. 在系统托盘中找到应用图标
8. 双击托盘图标，应用窗口应该重新显示
9. 右键托盘图标，应该显示菜单（显示窗口、隐藏窗口、退出应用）

### 验证功能正常工作

从控制台输出可以看到：
- "开机自启动设置已应用: false" - 说明开机自启动功能正在工作
- 应用正常启动，没有错误

## 注意事项

1. **开机自启动**在开发模式下可能不会完全生效，建议在打包后的应用中测试
2. **系统托盘**功能在不同操作系统上的表现可能略有不同
3. 如果托盘图标不显示，请检查图标文件路径是否正确
4. 设置变更会立即生效，无需重启应用

## 技术实现细节

- 使用 `app.setLoginItemSettings()` 设置开机自启动
- 使用 `new Tray()` 创建系统托盘
- 通过 IPC 通信实现设置的实时应用
- 使用 `app.isQuiting` 标志区分正常关闭和强制退出
